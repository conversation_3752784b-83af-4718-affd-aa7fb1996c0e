
#
import httpx

#
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

def fetch_url(url: str, console: Console) -> str:
    """
    Fetch content from a URL using httpx.

    Args:
        url (str): The URL to fetch
        console (Console): Rich console instance for output

    Returns:
        str: The response content as text
    """
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
            transient=True,
        ) as progress:
            task = progress.add_task(f"Fetching {url}...", total=None)

            with httpx.Client() as client:
                response = client.get(url)
                response.raise_for_status()
                progress.update(task, completed=True)

        console.print(f"✅ Successfully fetched content from {url}", style="green")
        return response.text

    except httpx.RequestError as e:
        console.print(f"❌ Network error while requesting {url}: {e}", style="red")
        return ""
    except httpx.HTTPStatusError as e:
        console.print(f"❌ HTTP error {e.response.status_code} while requesting {url}", style="red")
        return ""

def main():
    console = Console()

    console.print(Panel.fit(
        "[bold blue]HTML2MD URL Fetcher[/bold blue]\n"
        "Using httpx and Rich for beautiful console output",
        title="🚀 Welcome",
        border_style="blue"
    ))

    url = "https://zonetuto.fr/"
    console.print(f"\n🌐 Target URL: [cyan]{url}[/cyan]")

    content = fetch_url(url, console)
    if content:
        console.print("\n📄 Response Content:", style="bold green")
        console.print(Panel(
            content[:500] + ("..." if len(content) > 500 else ""),
            title="📋 Response Preview (first 500 chars)",
            border_style="green",
            expand=False
        ))
        console.print(f"\n📊 Total response length: [yellow]{len(content)}[/yellow] characters")
    else:
        console.print("\n❌ Failed to fetch content", style="bold red")

if __name__ == "__main__":
    main()
